.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #f1f5f9;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  z-index: 1001;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e2e8f0;
  color: #1e293b;
}

.modal-body {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 2rem;
}

.modal-image {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 8px;
  padding: 2rem;
  min-height: 250px;
  max-height: 300px;
}

.modal-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.modal-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  padding-right: 3rem;
}

.modal-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2563eb;
  margin: 0 0 0.75rem 0;
}

.modal-section p {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

.specifications-table {
  background: #f8fafc;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 1rem;
}

.spec-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.spec-row:last-child {
  border-bottom: none;
}

.spec-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.spec-value {
  color: #64748b;
  font-size: 0.9rem;
  text-align: right;
}

/* Removed unused styles for simplified modal */

/* Responsive Design */
@media (max-width: 768px) {
  .modal-backdrop {
    padding: 0.5rem;
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .modal-body {
    gap: 1rem;
    padding: 1.5rem;
  }

  .modal-title {
    font-size: 1.5rem;
    padding-right: 2.5rem;
  }

  .modal-image {
    min-height: 200px;
    padding: 1.5rem;
  }

  .specifications-table {
    margin-top: 0.5rem;
  }

  .spec-row {
    padding: 0.5rem 0.75rem;
  }

  .spec-label,
  .spec-value {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .modal-backdrop {
    padding: 0.25rem;
  }
  
  .modal-body {
    padding: 1rem;
    gap: 1rem;
  }
  
  .modal-title {
    font-size: 1.3rem;
  }
  
  .modal-close {
    width: 35px;
    height: 35px;
    font-size: 1.3rem;
  }
  
  .modal-image {
    min-height: 150px;
    padding: 1rem;
  }
  
  .modal-specifications {
    padding: 0.75rem;
  }
}
