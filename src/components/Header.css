.header {
  background: white;
  color: #2563eb;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: calc(100vw - 100px);
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-img {
  width: 200px;
  height: auto;
}

.logo-text h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: bold;
  color: #2563eb;
  letter-spacing: -0.5px;
}

.logo-text span {
  font-size: 0.9rem;
  color: #2563eb;
  display: block;
  margin-top: -4px;
  font-weight: 500;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  gap: 2rem;
}

.nav-item {
  background: none;
  border: none;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  text-decoration: none;
  display: inline-block;
}

.nav-item:hover {
  color: #2563eb;
  outline: none;
}

.nav-item.active {
  color: #2563eb;
  outline: none;
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.hamburger {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  position: relative;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background: #2563eb;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.hamburger span:nth-child(2) {
  margin: 6px 0;
}

.hamburger.open span:first-child {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.open span:nth-child(2) {
  opacity: 0;
}

.hamburger.open span:last-child {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  position: fixed;
  top: 0;
  right: -100%;
  width: 280px;
  height: 100vh;
  background: white;
  padding: 6rem 2rem 2rem;
  transition: right 0.3s ease;
  z-index: 1000;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
}

.mobile-nav.open {
  right: 0;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  background: none;
  border: none;
  color: #333;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
  text-decoration: none;
}

.mobile-nav-item:hover {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.05);
  transform: translateX(5px);
}

.mobile-nav-item.active {
  color: #2563eb;
  background: rgba(37, 99, 235, 0.1);
}

.nav-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.nav-label {
  flex: 1;
  text-align: left;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 0.2rem;
    max-width: calc(100vw - 50px);
  }

  .desktop-nav {
    display: none;
  }

  .mobile-menu-btn {
    display: block;
  }

  .mobile-nav {
    display: block;
  }

  .logo-text h1 {
    font-size: 1.2rem;
  }

  .logo-text span {
    font-size: 0.7rem;
  }

  .logo-img {
    width: 140px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 0.2rem;
    max-width: calc(100vw - 30px);
  }

  .logo {
    gap: 0.5rem;
  }

  .logo-text h1 {
    font-size: 1rem;
  }

  .logo-text span {
    font-size: 0.6rem;
  }

  .logo-img {
    width: 125px;
    height: 35px;
  }
}
