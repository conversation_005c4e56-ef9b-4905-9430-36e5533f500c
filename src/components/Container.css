.container {
  /* min-height: calc(100vh - 140px); Adjust based on header and footer height */
  background: #ffffff;
}

.container-content {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .container-content {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container-content {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-content {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container-content {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container-content {
    max-width: 1440px;
  }
}

@media (max-width: 768px) {
  .container-content {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

@media (max-width: 480px) {
  .container-content {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}
