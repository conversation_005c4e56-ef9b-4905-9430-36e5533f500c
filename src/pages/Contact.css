.contact {
  width: 100%;
}

.contact-header {
  text-align: start;
  margin-bottom: 3rem;
}

.contact-title {
  font-size: 3rem;
  font-weight: 400;
  color: #2563eb;
  margin-bottom: 1rem;
}

.contact-subtitle {
  font-size: 1.1rem;
  color: #333;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.contact-info-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-form-section {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.contact-form {
  width: 100%;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.contact-info-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-info,
.location-info {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.contact-info h2,
.location-info h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.info-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.info-value {
  color: #333;
  font-size: 0.95rem;
}

.address {
  color: #333;
  line-height: 1.6;
}

.address p {
  margin: 0 0 0.25rem 0;
}

.map-placeholder {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.map-container {
  height: 250px;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border: 2px dashed #94a3b8;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .contact-form-section {
    padding: 2rem;
  }
  
  .contact-info,
  .location-info {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .contact-title {
    font-size: 2.2rem;
  }
  
  .contact-subtitle {
    font-size: 1rem;
  }
  
  .contact-form-section {
    padding: 1.5rem;
  }
  
  .contact-info,
  .location-info {
    padding: 1.2rem;
  }
  
  .contact-info h2,
  .location-info h2 {
    font-size: 1.3rem;
  }
  
  .map-container {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .contact-title {
    font-size: 1.8rem;
  }
  
  .contact-subtitle {
    font-size: 0.9rem;
  }
  
  .contact-form-section {
    padding: 1rem;
  }
  
  .contact-info,
  .location-info {
    padding: 1rem;
  }
  
  .form-group input,
  .form-group textarea {
    padding: 0.625rem;
    font-size: 0.9rem;
  }
  
  .submit-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .contact-info h2,
  .location-info h2 {
    font-size: 1.2rem;
  }
  
  .info-item,
  .address p {
    font-size: 0.9rem;
  }
  
  .map-container {
    height: 180px;
  }
}
