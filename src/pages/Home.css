.home {
  width: 100%;
}

/* Hero Section */
.hero {
  background: #ffffff;
  padding: 2rem 0;
  margin-bottom: 3rem;
}

.hero-content {
  text-align: center;
  max-width: 1000px;
  margin: 0 auto;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.hero-image {
  margin-top: 1rem;
}

.hero-image img {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Our Products Section */
.our-products {
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2563eb;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1rem;
  color: #64748b;
  margin-bottom: 1rem;
  max-width: 600px;
}

.products-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 4rem;
  align-items: start;
}

.products-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.products-list {
  padding: 0rem 2rem 0;
  padding-left: 2rem;
}

.product-categories {
  margin-bottom: 2rem;
}

.category-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  color: #4b5563;
}

.bullet {
  color: #2563eb;
  font-weight: bold;
  font-size: 1.2rem;
}

.products-description {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.6;
  margin-top: 1rem;
}

/* About Navkala Section */
.about-navkala {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: start;
}

.about-text h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2563eb;
  margin-bottom: 1.5rem;
}

.about-text p {
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 1.2rem;
  font-size: 0.95rem;
}

.about-text p:last-child {
  margin-bottom: 0;
}

.about-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}



/* Responsive Design */
@media (max-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .products-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .products-list {
    order: 1;
  }

  .products-image {
    order: 2;
  }

  .products-list {
    padding-left: 0;
  }

  .about-navkala {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 2rem 0;
    margin-bottom: 2rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.6rem;
  }

  .about-navkala {
    padding: 1.5rem;
  }

  .about-text h2 {
    font-size: 1.5rem;
  }

  .category-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .products-description {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 1.5rem 0;
    margin-bottom: 1.5rem;
  }

  .hero-title {
    font-size: 1.6rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .about-navkala {
    padding: 1rem;
  }

  .about-text h2 {
    font-size: 1.3rem;
  }

  .about-text p {
    font-size: 0.85rem;
  }

  .category-item {
    font-size: 0.85rem;
  }

  .products-description {
    font-size: 0.8rem;
  }
}
